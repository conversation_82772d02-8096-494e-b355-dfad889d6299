#!/usr/bin/env python3
"""
Test script for receptionist appointment visibility issue
Tests the real scenario where receptionist logs in and sees only manager appointments
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://***************:3005/api"
RECEPTIONIST_EMAIL = "<EMAIL>"
RECEPTIONIST_PASSWORD = "1234"

def test_receptionist_login():
    """Test receptionist login and return token"""
    print("=== Testing Receptionist Login ===")
    
    login_url = f"{BASE_URL}/users/signin"
    login_data = {
        "email": RECEPTIONIST_EMAIL,
        "password": RECEPTIONIST_PASSWORD
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ Login successful")
        print(f"User: {data.get('user', {}).get('profile', {}).get('firstName', 'Unknown')} {data.get('user', {}).get('profile', {}).get('lastName', 'Unknown')}")
        print(f"Title: {data.get('user', {}).get('profile', {}).get('title', 'Unknown')}")
        print(f"Hospital: {data.get('user', {}).get('profile', {}).get('hospital', {}).get('name', 'Unknown')}")
        
        return data.get('token')
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Login failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_real_scenario_with_filter(token):
    """Test the exact scenario from the real frontend - with doctor filter"""
    print("\n=== Testing Real Scenario (With Manager Doctor Filter) ===")
    
    appointments_url = f"{BASE_URL}/appointments/getAppointments"
    headers = {"Authorization": f"Bearer {token}"}
    
    # Exact payload from the real frontend
    test_data = {
        "states": ["APPROVED", "INPROGRESS", "CANCELED", "ALMOST_COMPLETED"],
        "date": "2025-06-11",
        "page": 1,
        "limit": 10,
        "patient": "",
        "doctors": ["682b029a2fa88e753eb8f746"]  # Manager doctor profile ID
    }
    
    try:
        response = requests.post(appointments_url, json=test_data, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ Request successful")
        print(f"Total appointments found: {data.get('total', 0)}")
        print(f"Appointments on page: {len(data.get('docs', []))}")
        
        # Show details of found appointments
        for i, appointment in enumerate(data.get('docs', [])):
            doctor = appointment.get('doctor', {})
            patient = appointment.get('patient', {})
            print(f"  Appointment {i+1}:")
            print(f"    Doctor: {doctor.get('firstName', 'Unknown')} {doctor.get('lastName', 'Unknown')} (Profile ID: {doctor.get('_id', 'Unknown')})")
            print(f"    Patient: {patient.get('firstName', 'Unknown')} {patient.get('lastName', 'Unknown')}")
            print(f"    State: {appointment.get('state', 'Unknown')}")
            print(f"    Date: {appointment.get('date', 'Unknown')}")
        
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

def test_without_doctor_filter(token):
    """Test without doctor filter - should show all doctors"""
    print("\n=== Testing Without Doctor Filter (Should Show All Doctors) ===")
    
    appointments_url = f"{BASE_URL}/appointments/getAppointments"
    headers = {"Authorization": f"Bearer {token}"}
    
    test_data = {
        "states": ["APPROVED", "INPROGRESS", "CANCELED", "ALMOST_COMPLETED"],
        "date": "2025-06-11",
        "page": 1,
        "limit": 10,
        "patient": ""
        # No doctors filter - should show all doctors
    }
    
    try:
        response = requests.post(appointments_url, json=test_data, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ Request successful")
        print(f"Total appointments found: {data.get('total', 0)}")
        print(f"Appointments on page: {len(data.get('docs', []))}")
        
        # Collect unique doctors
        unique_doctors = {}
        for appointment in data.get('docs', []):
            doctor = appointment.get('doctor', {})
            doctor_id = doctor.get('_id')
            if doctor_id and doctor_id not in unique_doctors:
                unique_doctors[doctor_id] = {
                    'name': f"{doctor.get('firstName', 'Unknown')} {doctor.get('lastName', 'Unknown')}",
                    'title': doctor.get('title', 'Unknown')
                }
        
        print(f"Unique doctors found: {len(unique_doctors)}")
        for doctor_id, doctor_info in unique_doctors.items():
            print(f"  - {doctor_info['name']} ({doctor_info['title']}) - Profile ID: {doctor_id}")
        
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

def test_with_different_doctor_filter(token):
    """Test with a different doctor filter to see if receptionist can access other doctors"""
    print("\n=== Testing With Different Doctor Filter ===")
    
    appointments_url = f"{BASE_URL}/appointments/getAppointments"
    headers = {"Authorization": f"Bearer {token}"}
    
    # Try with a different doctor profile ID (from the test results we saw earlier)
    test_data = {
        "states": ["APPROVED", "INPROGRESS", "CANCELED", "ALMOST_COMPLETED"],
        "date": "2025-06-11",
        "page": 1,
        "limit": 10,
        "patient": "",
        "doctors": ["682c85572fa88e1d85b8f75d"]  # Different doctor profile ID
    }
    
    try:
        response = requests.post(appointments_url, json=test_data, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        print(f"✅ Request successful")
        print(f"Total appointments found: {data.get('total', 0)}")
        print(f"Appointments on page: {len(data.get('docs', []))}")
        
        # Show details of found appointments
        for i, appointment in enumerate(data.get('docs', [])):
            doctor = appointment.get('doctor', {})
            patient = appointment.get('patient', {})
            print(f"  Appointment {i+1}:")
            print(f"    Doctor: {doctor.get('firstName', 'Unknown')} {doctor.get('lastName', 'Unknown')} (Profile ID: {doctor.get('_id', 'Unknown')})")
            print(f"    Patient: {patient.get('firstName', 'Unknown')} {patient.get('lastName', 'Unknown')}")
            print(f"    State: {appointment.get('state', 'Unknown')}")
        
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

def main():
    """Main test function"""
    print("🧪 Testing Real Receptionist Scenario")
    print("=" * 60)
    
    # Test login
    token = test_receptionist_login()
    if not token:
        print("❌ Cannot proceed without valid token")
        sys.exit(1)
    
    # Test real scenario with manager doctor filter
    real_result = test_real_scenario_with_filter(token)
    
    # Test without doctor filter
    all_result = test_without_doctor_filter(token)
    
    # Test with different doctor filter
    different_result = test_with_different_doctor_filter(token)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 ANALYSIS")
    print("=" * 60)
    
    print("\n🔍 ISSUE ANALYSIS:")
    print("The problem is that the frontend is sending a specific doctor filter:")
    print("  'doctors': ['682b029a2fa88e753eb8f746']")
    print("This restricts results to only the manager doctor.")
    print("\n💡 SOLUTION:")
    print("The receptionist should be able to:")
    print("1. See ALL doctors when no filter is applied")
    print("2. Filter by ANY doctor in the hospital")
    print("3. Not be restricted to only the manager doctor")
    
    if all_result and all_result.get('total', 0) > 1:
        print("\n✅ GOOD NEWS: Our backend fix is working!")
        print("   When no doctor filter is applied, receptionist can see multiple doctors")
    else:
        print("\n⚠️  ISSUE: Backend fix may need adjustment")
    
    if different_result and different_result.get('total', 0) > 0:
        print("✅ GOOD NEWS: Receptionist can filter by other doctors!")
    else:
        print("⚠️  ISSUE: Receptionist cannot filter by other doctors")

if __name__ == "__main__":
    main()
