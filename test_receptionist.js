const fetch = require('node-fetch');

async function testReceptionist() {
    try {
        console.log('=== Testing Receptionist Login ===');
        
        // Login as receptionist
        const loginResponse = await fetch('http://192.168.101.176:3005/api/auth/signIn', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: '1234'
            })
        });
        
        const loginData = await loginResponse.json();
        console.log('Login response:', JSON.stringify(loginData, null, 2));
        
        if (!loginData.token) {
            console.error('Login failed!');
            return;
        }
        
        const token = loginData.token;
        console.log('Login successful, token:', token.substring(0, 50) + '...');
        
        console.log('\n=== Testing Get Appointments ===');
        
        // Test getting appointments
        const appointmentsResponse = await fetch('http://192.168.101.176:3005/api/appointments/getAppointments', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                states: ["APPROVED", "INPROGRESS", "CANCELED", "ALMOST_COMPLETED"],
                date: "2025-06-11",
                page: 1,
                limit: 10,
                patient: "",
                doctors: ["682b029a2fa88e753eb8f746"]
            })
        });
        
        const appointmentsData = await appointmentsResponse.json();
        console.log('Appointments response:', JSON.stringify(appointmentsData, null, 2));
        
        console.log('\n=== Testing Get All Appointments (no doctor filter) ===');
        
        // Test getting all appointments without doctor filter
        const allAppointmentsResponse = await fetch('http://192.168.101.176:3005/api/appointments/getAppointments', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                states: ["APPROVED", "INPROGRESS", "CANCELED", "ALMOST_COMPLETED"],
                date: "2025-06-11",
                page: 1,
                limit: 10,
                patient: ""
            })
        });
        
        const allAppointmentsData = await allAppointmentsResponse.json();
        console.log('All appointments response:', JSON.stringify(allAppointmentsData, null, 2));
        
    } catch (error) {
        console.error('Error:', error);
    }
}

testReceptionist();
